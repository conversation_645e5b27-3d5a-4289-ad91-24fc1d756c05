#include "Tokenizer.h"
#include <iostream>

std::string Token::to_string() const {
    std::string type_str;
    switch (type) {
        case END_OF_FILE: type_str = "END_OF_FILE"; break;
        case IDENTIFIER: type_str = "IDENTIFIER"; break;
        case NUMBER: type_str = "NUMBER"; break;
        case PLUS: type_str = "PLUS"; break;
        case MINUS: type_str = "MINUS"; break;
        case MULTIPLY: type_str = "MULTIPLY"; break;
        case DIVIDE: type_str = "DIVIDE"; break;
        case ASSIGN: type_str = "ASSIGN"; break;
        case SEMICOLON: type_str = "SEMICOLON"; break;
        case LPAREN: type_str = "LPAREN"; break;
        case RPAREN: type_str = "RPAREN"; break;
        case LBRACE: type_str = "LBRACE"; break;
        case RBRACE: type_str = "RBRACE"; break;
        case KEYWORD_LET: type_str = "KEYWORD_LET"; break;
        case KEYWORD_PRINT: type_str = "KEYWORD_PRINT"; break;
        case KEYWORD_TRUE: type_str = "KEYWORD_TRUE"; break;
        case KEYWORD_FALSE: type_str = "KEYWORD_FALSE"; break;
        case EQ_EQ: type_str = "EQ_EQ"; break;
        case NEQ: type_str = "NEQ"; break;
        case LT: type_str = "LT"; break;
        case GT: type_str = "GT"; break;
        case LTE: type_str = "LTE"; break;
        case GTE: type_str = "GTE"; break;
        case KEYWORD_IF: type_str = "KEYWORD_IF"; break;
        case KEYWORD_ELSE: type_str = "KEYWORD_ELSE"; break;
        case KEYWORD_WHILE: type_str = "KEYWORD_WHILE"; break;
        case STRING_LITERAL: type_str = "STRING_LITERAL"; break;
    }
    return "Token(" + type_str + ", \"" + value + "\")";
}

Tokenizer::Tokenizer(const std::string& code) : code_(code), current_pos_(0) {}

void Tokenizer::skip_whitespace() {
    while (current_pos_ < code_.length()) {
        char current_char = code_[current_pos_];
        if (std::isspace(current_char)) {
            current_pos_++;
        } else if (current_char == '/' && current_pos_ + 1 < code_.length()) {
            char next_char = code_[current_pos_ + 1];
            if (next_char == '/') { // Single-line comment
                while (current_pos_ < code_.length() && code_[current_pos_] != '\n') {
                    current_pos_++;
                }
            } else if (next_char == '*') { // Multi-line comment
                current_pos_ += 2; // Consume /*
                while (current_pos_ + 1 < code_.length() && !(code_[current_pos_] == '*' && code_[current_pos_ + 1] == '/')) {
                    current_pos_++;
                }
                if (current_pos_ + 1 < code_.length()) {
                    current_pos_ += 2; // Consume */
                }
            } else {
                break; // Not a comment, break from whitespace/comment skipping
            }
        } else {
            break; // Not whitespace or comment, stop skipping
        }
    }
}

Token Tokenizer::read_number() {
    std::string value;
    while (current_pos_ < code_.length() && std::isdigit(code_[current_pos_])) {
        value += code_[current_pos_];
        current_pos_++;
    }
    return {NUMBER, value};
}

Token Tokenizer::read_identifier_or_keyword() {
    std::string value;
    while (current_pos_ < code_.length() && (std::isalnum(code_[current_pos_]) || code_[current_pos_] == '_')) {
        value += code_[current_pos_];
        current_pos_++;
    }
    if (value == "let") return {KEYWORD_LET, value};
    if (value == "print") return {KEYWORD_PRINT, value};
    if (value == "true") return {KEYWORD_TRUE, value};
    if (value == "false") return {KEYWORD_FALSE, value};
    if (value == "if") return {KEYWORD_IF, value};
    if (value == "else") return {KEYWORD_ELSE, value};
    if (value == "while") return {KEYWORD_WHILE, value};
    if (value == "function") return {KEYWORD_FUNCTION, value};
    if (value == "return") return {KEYWORD_RETURN, value};
    return {IDENTIFIER, value};
}

Token Tokenizer::read_string_literal() {
    std::string value;
    current_pos_++; // Consume the opening quote
    while (current_pos_ < code_.length() && code_[current_pos_] != '\'') {
        // Basic escape sequence handling (e.g., \n, \t, \" could be added)
        if (code_[current_pos_] == '\\' && current_pos_ + 1 < code_.length()) {
            char escaped_char = code_[current_pos_ + 1];
            switch (escaped_char) {
                case 'n': value += '\n'; break;
                case 't': value += '\t'; break;
                case '\'': value += '\''; break;
                case '\\': value += '\\'; break;
                default: value += escaped_char; // For unsupported escapes, just add the char
            }
            current_pos_ += 2; // Consume backslash and escaped char
        } else {
            value += code_[current_pos_];
            current_pos_++;
        }
    }
    if (current_pos_ >= code_.length()) {
        throw std::runtime_error("Syntax Error: Unterminated string literal");
    }
    current_pos_++; // Consume the closing quote
    return {STRING_LITERAL, value};
}

Token Tokenizer::read_operator() {
    char current_char = code_[current_pos_];
    char next_char = (current_pos_ + 1 < code_.length()) ? code_[current_pos_ + 1] : '\0';

    if (current_char == '=' && next_char == '=') { current_pos_ += 2; return {EQ_EQ, "=="}; }
    if (current_char == '!' && next_char == '=') { current_pos_ += 2; return {NEQ, "!="}; }
    if (current_char == '<' && next_char == '=') { current_pos_ += 2; return {LTE, "<="}; }
    if (current_char == '>' && next_char == '=') { current_pos_ += 2; return {GTE, ">="}; }
    if (current_char == '&' && next_char == '&') { current_pos_ += 2; return {AND_AND, "&&"}; }
    if (current_char == '|' && next_char == '|') { current_pos_ += 2; return {OR_OR, "||"}; }

    // Single character operators
    switch (current_char) {
        case '+': current_pos_++; return {PLUS, "+"};
        case '-': current_pos_++; return {MINUS, "-"};
        case '*': current_pos_++; return {MULTIPLY, "*"};
        case '/': current_pos_++; return {DIVIDE, "/"};
        case '=': current_pos_++; return {ASSIGN, "="};
        case ';': current_pos_++; return {SEMICOLON, ";"};
        case '(': current_pos_++; return {LPAREN, "("};
        case ')': current_pos_++; return {RPAREN, ")"};
        case '{': current_pos_++; return {LBRACE, "{"};
        case '}': current_pos_++; return {RBRACE, "}"};
        case '<': current_pos_++; return {LT, "<"};
        case '>': current_pos_++; return {GT, ">"};
        case '!': current_pos_++; return {NOT, "!"};
        default: return {END_OF_FILE, ""}; // Should not be reached if called correctly
    }
}

Token Tokenizer::get_next_token() {
    skip_whitespace();

    if (current_pos_ >= code_.length()) {
        return {END_OF_FILE, ""};
    }

    char current_char = code_[current_pos_];

    if (std::isdigit(current_char)) {
        return read_number();
    }

    if (current_char == '\'') {
        return read_string_literal();
    }

    if (std::isalpha(current_char) || current_char == '_') {
        return read_identifier_or_keyword();
    }

    // Check for multi-character operators first
    if (current_char == '=' || current_char == '!' || current_char == '<' || current_char == '>' || current_char == '&' || current_char == '|') {
        if (current_pos_ + 1 < code_.length() && 
           ((current_char == '=' && code_[current_pos_ + 1] == '=') ||
            (current_char == '!' && code_[current_pos_ + 1] == '=') ||
            (current_char == '<' && code_[current_pos_ + 1] == '=') ||
            (current_char == '>' && code_[current_pos_ + 1] == '=') ||
            (current_char == '&' && code_[current_pos_ + 1] == '&') ||
            (current_char == '|' && code_[current_pos_ + 1] == '|'))) {
            return read_operator();
        }
    }

    // Single character operators and braces
    switch (current_char) {
        case '+': current_pos_++; return {PLUS, "+"};
        case '-': current_pos_++; return {MINUS, "-"};
        case '*': current_pos_++; return {MULTIPLY, "*"};
        case '/': current_pos_++; return {DIVIDE, "/"};
        case '=': current_pos_++; return {ASSIGN, "="};
        case ';': current_pos_++; return {SEMICOLON, ";"};
        case '(': current_pos_++; return {LPAREN, "("};
        case ')': current_pos_++; return {RPAREN, ")"};
        case '{': current_pos_++; return {LBRACE, "{"};
        case '}': current_pos_++; return {RBRACE, "}"};
        case '<': current_pos_++; return {LT, "<"};
        case '>': current_pos_++; return {GT, ">"};
        case '!': current_pos_++; return {NOT, "!"};
        case ',': current_pos_++; return {COMMA, ","};
        default: 
            std::cerr << "Error: Unexpected character '" << current_char << "' at position " << current_pos_ << std::endl;
            current_pos_++;
            return {END_OF_FILE, ""};
    }
}

void Tokenizer::set_current_pos(size_t pos) {
    current_pos_ = pos;
}

size_t Tokenizer::get_current_pos() const {
    return current_pos_;
}

const std::string& Tokenizer::get_code() const {
    return code_;
}
