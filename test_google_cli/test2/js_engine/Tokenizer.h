#ifndef TOKENIZER_H
#define TOKE<PERSON>ZER_H

#include <string>
#include <vector>
#include <memory>
#include "../utilities/Logger.h"
#include "../utilities/ErrorHandler.h"

enum TokenType {
    END_OF_FILE = 0,
    IDENTIFIER,
    NUMBER,
    PLUS,
    MINUS,
    MULTIPLY,
    DIVIDE,
    ASSIGN,
    SEMICOLON,
    LPAREN,
    RPAREN,
    LBRACE, // {
    RBRACE, // }
    KEYWORD_LET,
    KEYWORD_PRINT,
    KEYWORD_TRUE,
    KEYWORD_FALSE,
    EQ_EQ,  // ==
    NEQ,    // !=
    LT,     // <
    GT,     // >
    LTE,    // <=
    GTE,    // >=
    KEYWORD_IF,
    KEYWORD_ELSE,
    KEYWORD_WHILE,
    STRING_LITERAL,
    KEYWORD_FUNCTION,
    COMMA,
    KEYWORD_RETURN,
    AND_AND, // &&
    OR_OR,   // ||
    NOT      // !
};

struct Token {
    TokenType type;
    std::string value;

    std::string to_string() const;
};

class Tokenizer {
public:
    Tokenizer(const std::string& code);
    Token get_next_token();
    void set_current_pos(size_t pos);
    size_t get_current_pos() const;
    const std::string& get_code() const;

    // Error handling and validation methods
    bool is_valid_state() const;
    void validate_position(size_t pos) const;
    void reset_state();

private:
    std::string code_;
    size_t current_pos_;
    std::shared_ptr<utilities::Logger> logger_;
    std::shared_ptr<utilities::ErrorHandler> error_handler_;

    // Safety flags
    bool initialized_;
    size_t max_safe_position_;

    void skip_whitespace();
    Token read_number();
    Token read_identifier_or_keyword();
    Token read_string_literal();
    Token read_operator();

    // Enhanced error handling methods
    void log_tokenizer_state(const std::string& operation) const;
    void handle_tokenizer_error(const std::string& error_msg, const std::string& context = "") const;
    bool is_safe_to_access(size_t pos) const;
    char safe_char_at(size_t pos) const;
};

#endif // TOKENIZER_H
