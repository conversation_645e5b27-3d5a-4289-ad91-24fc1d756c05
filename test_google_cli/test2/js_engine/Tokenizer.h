#ifndef TOKENIZER_H
#define TOKENIZER_H

#include <string>
#include <vector>

enum TokenType {
    END_OF_FILE = 0,
    IDENTIFIER,
    NUMBER,
    PLUS,
    MINUS,
    MULTIPLY,
    DIVIDE,
    ASSIGN,
    SEMICOLON,
    LPAREN,
    RPAREN,
    LBRACE, // {
    RBRACE, // }
    KEYWORD_LET,
    KEYWORD_PRINT,
    KEYWORD_TRUE,
    KEYWORD_FALSE,
    EQ_EQ,  // ==
    NEQ,    // !=
    LT,     // <
    GT,     // >
    LTE,    // <=
    GTE,    // >=
    KEYWORD_IF,
    KEYWORD_ELSE,
    KEYWORD_WHILE,
    STRING_LITERAL,
    KEYWORD_FUNCTION,
    COMMA,
    KEYWORD_RETURN,
    AND_AND, // &&
    OR_OR,   // ||
    NOT      // !
};

struct Token {
    TokenType type;
    std::string value;

    std::string to_string() const;
};

class Tokenizer {
public:
    Tokenizer(const std::string& code);
    Token get_next_token();
    void set_current_pos(size_t pos);
    size_t get_current_pos() const;
    const std::string& get_code() const;

private:
    std::string code_;
    size_t current_pos_;

    void skip_whitespace();
    Token read_number();
    Token read_identifier_or_keyword();
    Token read_string_literal();
    Token read_operator();
};

#endif // TOKENIZER_H
