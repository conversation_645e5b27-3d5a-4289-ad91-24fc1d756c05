#ifndef INTERPRETER_H
#define INTERPRETER_H

#include "Tokenizer.h"
#include <string>
#include <map>
#include <vector>
#include <stdexcept>
#include <variant> // For std::variant (C++17) - if not available, will use custom union/enum
#include <functional> // For std::function

// Custom Value type to hold either int or string
enum ValueType {
    INT_TYPE,
    STRING_TYPE,
    BOOL_TYPE, // Booleans are still ints (0 or 1) but conceptually distinct
    VOID_TYPE // For functions that don't explicitly return a value
};

struct Value {
    ValueType type;
    int int_value;
    std::string string_value;

    // Constructors
    Value() : type(VOID_TYPE), int_value(0) {} // Default to VOID
    Value(int val) : type(INT_TYPE), int_value(val) {}
    Value(std::string val) : type(STRING_TYPE), string_value(std::move(val)) {}
    Value(bool val) : type(BOOL_TYPE), int_value(val ? 1 : 0) {}

    // Helper to convert to int (for boolean/arithmetic contexts)
    int as_int() const {
        if (type == INT_TYPE || type == BOOL_TYPE) return int_value;
        throw std::runtime_error("Type Error: Cannot convert " + std::to_string(type) + " to int");
    }

    // Helper to convert to string
    std::string as_string() const {
        if (type == STRING_TYPE) return string_value;
        if (type == INT_TYPE) return std::to_string(int_value);
        if (type == BOOL_TYPE) return (int_value == 1 ? "true" : "false");
        if (type == VOID_TYPE) return "undefined"; // JavaScript's undefined
        return ""; // Should not happen
    }

    // Equality comparison for Value (simplified)
    bool operator==(const Value& other) const {
        if (type != other.type) {
            // Simplified type coercion for comparison (e.g., 0 == false)
            if ((type == INT_TYPE || type == BOOL_TYPE) && (other.type == INT_TYPE || other.type == BOOL_TYPE)) {
                return int_value == other.int_value;
            }
            return false;
        }
        if (type == INT_TYPE || type == BOOL_TYPE) return int_value == other.int_value;
        if (type == STRING_TYPE) return string_value == other.string_value;
        return false;
    }
};

// Structure to hold function definition
struct FunctionDefinition {
    std::vector<std::string> parameters;
    size_t body_start_pos; // Position in the code string where the function body starts
    size_t body_end_pos;   // Position in the code string where the function body ends
};

class Interpreter {
public:
    Interpreter();
    std::string interpret(const std::string& code, std::function<void(const std::string&)> print_callback);

private:
    // Stack of variable scopes (each map represents a scope)
    std::vector<std::map<std::string, Value>> call_stack_;
    std::map<std::string, FunctionDefinition> function_definitions_;

    Tokenizer* tokenizer_;
    Token current_token_;
    std::function<void(const std::string&)> print_callback_;
    bool returned_; // Flag to indicate if a return statement was executed
    Value return_value_; // Value returned by a function

    void eat(TokenType type);
    Value parse_expression();
    Value parse_term();
    Value parse_factor();
    Value parse_boolean_expression();
    void parse_statement();
    void parse_block();
    void parse_program();
    void handle_print_statement();
    void handle_if_statement();
    void handle_while_statement();
    void parse_function_declaration(); // New: for function declarations
    Value parse_function_call(const std::string& function_name); // New: for function calls
    void handle_return_statement(); // New: for return statements
    void consume_block(); // New: to consume a block without executing it

    // Helper for variable lookup in current and outer scopes
    Value& get_variable(const std::string& name);
    bool has_variable(const std::string& name) const;
    void set_variable(const std::string& name, const Value& value);
};

#endif // INTERPRETER_H
