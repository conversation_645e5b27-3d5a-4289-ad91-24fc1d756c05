#include "Interpreter.h"
#include <iostream>
#include <stdexcept>

Interpreter::Interpreter() : tokenizer_(nullptr), returned_(false) {
    call_stack_.push_back({}); // Global scope
}

void Interpreter::eat(TokenType type) {
    if (current_token_.type == type) {
        current_token_ = tokenizer_->get_next_token();
    } else {
        throw std::runtime_error("Syntax Error: Expected " + current_token_.to_string() + ", got " + current_token_.to_string());
    }
}

Value& Interpreter::get_variable(const std::string& name) {
    // Search from current scope up to global scope
    for (auto it = call_stack_.rbegin(); it != call_stack_.rend(); ++it) {
        if (it->count(name)) {
            return it->at(name);
        }
    }
    throw std::runtime_error("Runtime Error: Undefined variable '" + name + "'");
}

bool Interpreter::has_variable(const std::string& name) const {
    for (auto it = call_stack_.rbegin(); it != call_stack_.rend(); ++it) {
        if (it->count(name)) {
            return true;
        }
    }
    return false;
}

void Interpreter::set_variable(const std::string& name, const Value& value) {
    // Assign to existing variable in any scope, or create in current scope
    for (auto it = call_stack_.rbegin(); it != call_stack_.rend(); ++it) {
        if (it->count(name)) {
            it->at(name) = value;
            return;
        }
    }
    // If not found in any parent scope, declare in current scope
    call_stack_.back()[name] = value;
}

Value Interpreter::parse_factor() {
    Token token = current_token_;
    if (token.type == MINUS) { // Unary minus
        eat(MINUS);
        Value operand = parse_factor();
        if (operand.type != INT_TYPE) {
            throw std::runtime_error("Type Error: Cannot apply unary minus to non-integer type");
        }
        return Value(-operand.as_int());
    } else if (token.type == NUMBER) {
        eat(NUMBER);
        return Value(std::stoi(token.value));
    } else if (token.type == STRING_LITERAL) {
        eat(STRING_LITERAL);
        return Value(token.value);
    } else if (token.type == IDENTIFIER) {
        std::string identifier_name = token.value;
        eat(IDENTIFIER);
        if (current_token_.type == LPAREN) { // Function call
            return parse_function_call(identifier_name);
        } else {
            // Variable access
            return get_variable(identifier_name);
        }
    } else if (token.type == KEYWORD_TRUE) {
        eat(KEYWORD_TRUE);
        return Value(true);
    } else if (token.type == KEYWORD_FALSE) {
        eat(KEYWORD_FALSE);
        return Value(false);
    } else if (token.type == LPAREN) {
        eat(LPAREN);
        Value result = parse_expression();
        eat(RPAREN);
        return result;
    } else {
        throw std::runtime_error("Syntax Error: Unexpected token in factor: " + token.to_string());
    }
}

Value Interpreter::parse_term() {
    Value result = parse_factor();

    while (current_token_.type == MULTIPLY || current_token_.type == DIVIDE) {
        if (result.type != INT_TYPE) {
            throw std::runtime_error("Type Error: Cannot perform arithmetic on non-integer type");
        }
        Token op_token = current_token_;
        if (op_token.type == MULTIPLY) {
            eat(MULTIPLY);
            Value right = parse_factor();
            if (right.type != INT_TYPE) {
                throw std::runtime_error("Type Error: Cannot perform arithmetic on non-integer type");
            }
            result = Value(result.as_int() * right.as_int());
        } else if (op_token.type == DIVIDE) {
            eat(DIVIDE);
            Value right = parse_factor();
            if (right.type != INT_TYPE) {
                throw std::runtime_error("Type Error: Cannot perform arithmetic on non-integer type");
            }
            if (right.as_int() == 0) {
                throw std::runtime_error("Runtime Error: Division by zero");
            }
            result = Value(result.as_int() / right.as_int());
        }
    }
    return result;
}

Value Interpreter::parse_expression() {
    Value result = parse_term();

    while (current_token_.type == PLUS || current_token_.type == MINUS) {
        Token op_token = current_token_;
        if (op_token.type == PLUS) {
            eat(PLUS);
            Value right = parse_term();
            if (result.type == STRING_TYPE || right.type == STRING_TYPE) {
                // String concatenation
                result = Value(result.as_string() + right.as_string());
            } else if (result.type == INT_TYPE && right.type == INT_TYPE) {
                // Integer addition
                result = Value(result.as_int() + right.as_int());
            } else {
                throw std::runtime_error("Type Error: Incompatible types for + operation");
            }
        } else if (op_token.type == MINUS) {
            eat(MINUS);
            Value right = parse_term();
            if (result.type != INT_TYPE || right.type != INT_TYPE) {
                throw std::runtime_error("Type Error: Cannot perform subtraction on non-integer type");
            }
            result = Value(result.as_int() - right.as_int());
        }
    }
    return result;
}

Value Interpreter::parse_boolean_expression() {
    Value result = parse_expression();

    // Handle NOT operator
    if (current_token_.type == NOT) {
        eat(NOT);
        result = Value(!result.as_int()); // Logical NOT on integer representation
    }

    // Handle comparison operators
    while (current_token_.type == EQ_EQ || current_token_.type == NEQ ||
           current_token_.type == LT || current_token_.type == GT ||
           current_token_.type == LTE || current_token_.type == GTE) {
        TokenType op_type = current_token_.type;
        eat(op_type);
        Value right_val = parse_expression();

        // Perform comparison based on integer representation
        int left = result.as_int();
        int right = right_val.as_int();

        switch (op_type) {
            case EQ_EQ:  result = Value(left == right); break;
            case NEQ:    result = Value(left != right); break;
            case LT:     result = Value(left < right); break;
            case GT:     result = Value(left > right); break;
            case LTE:    result = Value(left <= right); break;
            case GTE:    result = Value(left >= right); break;
            default: throw std::runtime_error("Syntax Error: Unknown comparison operator");
        }
    }

    // Handle logical AND and OR
    while (current_token_.type == AND_AND || current_token_.type == OR_OR) {
        TokenType op_type = current_token_.type;
        eat(op_type);
        Value right_val = parse_boolean_expression(); // Recursive call for right operand

        int left = result.as_int();
        int right = right_val.as_int();

        switch (op_type) {
            case AND_AND: result = Value(left && right); break;
            case OR_OR:   result = Value(left || right); break;
            default: throw std::runtime_error("Syntax Error: Unknown logical operator");
        }
    }

    return result;
}

void Interpreter::handle_print_statement() {
    eat(KEYWORD_PRINT);
    eat(LPAREN);
    Value value = parse_expression(); // Can print result of any expression
    eat(RPAREN);
    print_callback_(value.as_string() + "\n");
}

void Interpreter::parse_block() {
    eat(LBRACE);
    while (current_token_.type != RBRACE && current_token_.type != END_OF_FILE && !returned_) {
        parse_statement();
    }
    eat(RBRACE);
}

void Interpreter::handle_if_statement() {
    eat(KEYWORD_IF);
    eat(LPAREN);
    Value condition_val = parse_boolean_expression();
    int condition = condition_val.as_int();
    eat(RPAREN);

    if (condition) {
        parse_block(); // Execute the 'if' block
        if (current_token_.type == KEYWORD_ELSE) {
            eat(KEYWORD_ELSE);
            consume_block(); // Skip the 'else' block
        }
    } else {
        consume_block(); // Skip the 'if' block
        if (current_token_.type == KEYWORD_ELSE) {
            eat(KEYWORD_ELSE);
            parse_block(); // Execute the 'else' block
        }
    }
}

void Interpreter::handle_while_statement() {
    eat(KEYWORD_WHILE);
    eat(LPAREN);
    size_t condition_start_pos = tokenizer_->get_current_pos();
    Value condition_val = parse_boolean_expression();
    int condition = condition_val.as_int();
    eat(RPAREN);

    size_t block_start_pos = tokenizer_->get_current_pos();

    if (!condition) { // If initial condition is false, just consume the block once
        parse_block();
        return;
    }

    while (condition && !returned_) {
        tokenizer_->set_current_pos(block_start_pos);
        parse_block();

        if (returned_) break; // Exit if return was hit inside the block

        tokenizer_->set_current_pos(condition_start_pos);
        condition_val = parse_boolean_expression();
        condition = condition_val.as_int();
        eat(RPAREN);
    }
}

void Interpreter::parse_function_declaration() {
    eat(KEYWORD_FUNCTION);
    Token name_token = current_token_;
    eat(IDENTIFIER);
    std::string function_name = name_token.value;

    if (function_definitions_.count(function_name)) {
        throw std::runtime_error("Syntax Error: Function '" + function_name + "' already defined");
    }

    FunctionDefinition func_def;
    eat(LPAREN);
    while (current_token_.type == IDENTIFIER) {
        func_def.parameters.push_back(current_token_.value);
        eat(IDENTIFIER);
        if (current_token_.type == COMMA) {
            eat(COMMA);
        } else {
            break;
        }
    }
    eat(RPAREN);

    func_def.body_start_pos = tokenizer_->get_current_pos();

    // Temporarily save tokenizer state to find the end of the function body
    size_t saved_tokenizer_pos = tokenizer_->get_current_pos();
    Token saved_current_token = current_token_;

    // Consume the block to find its end position
    consume_block();
    func_def.body_end_pos = tokenizer_->get_current_pos();

    function_definitions_[function_name] = func_def;

    // Restore tokenizer state to continue parsing from after the function declaration
    tokenizer_->set_current_pos(saved_tokenizer_pos);
    current_token_ = saved_current_token;

    // Now, skip the function body for real in the main parsing flow
    consume_block();
}

Value Interpreter::parse_function_call(const std::string& function_name) {
    if (!function_definitions_.count(function_name)) {
        throw std::runtime_error("Runtime Error: Undefined function '" + function_name + "'");
    }

    FunctionDefinition func_def = function_definitions_[function_name];
    std::vector<Value> arguments;

    eat(LPAREN);
    while (current_token_.type != RPAREN) {
        arguments.push_back(parse_expression());
        if (current_token_.type == COMMA) {
            eat(COMMA);
        } else {
            break;
        }
    }
    eat(RPAREN);

    if (arguments.size() != func_def.parameters.size()) {
        throw std::runtime_error("Runtime Error: Function '" + function_name + "' expects " +
                                 std::to_string(func_def.parameters.size()) + " arguments, but got " +
                                 std::to_string(arguments.size()));
    }

    // Save current interpreter state
    size_t saved_tokenizer_pos = tokenizer_->get_current_pos();
    Token saved_current_token = current_token_;

    // Push new scope onto call stack
    call_stack_.push_back({});
    std::map<std::string, Value>& current_scope = call_stack_.back();

    // Bind arguments to parameters in the new scope
    for (size_t i = 0; i < func_def.parameters.size(); ++i) {
        current_scope[func_def.parameters[i]] = arguments[i];
    }

    // Execute function body
    tokenizer_->set_current_pos(func_def.body_start_pos);
    current_token_ = tokenizer_->get_next_token(); // Get first token of body
    returned_ = false; // Reset return flag for new function call
    return_value_ = Value(); // Reset return value

    parse_block(); // Execute the block

    // Pop scope from call stack
    call_stack_.pop_back();

    // Restore interpreter state
    tokenizer_->set_current_pos(saved_tokenizer_pos);
    current_token_ = saved_current_token;

    return return_value_; // Return the value (or void if no explicit return)
}

void Interpreter::handle_return_statement() {
    eat(KEYWORD_RETURN);
    if (current_token_.type != SEMICOLON) {
        return_value_ = parse_expression();
    }
    returned_ = true;
    // No eat(SEMICOLON) here, as parse_statement will eat it.
}

void Interpreter::consume_block() {
    eat(LBRACE);
    int brace_count = 1;
    while (brace_count > 0 && current_token_.type != END_OF_FILE) {
        if (current_token_.type == LBRACE) {
            brace_count++;
        } else if (current_token_.type == RBRACE) {
            brace_count--;
        }
        current_token_ = tokenizer_->get_next_token();
    }
    if (brace_count != 0) {
        throw std::runtime_error("Syntax Error: Unmatched braces in block consumption");
    }
}

void Interpreter::parse_statement() {
    if (returned_) return; // If a return statement was hit, stop processing statements in this block

    if (current_token_.type == KEYWORD_LET) {
        eat(KEYWORD_LET);
        Token id_token = current_token_;
        eat(IDENTIFIER);
        eat(ASSIGN);
        set_variable(id_token.value, parse_expression());
        eat(SEMICOLON);
    } else if (current_token_.type == KEYWORD_PRINT) {
        handle_print_statement();
        eat(SEMICOLON);
    } else if (current_token_.type == KEYWORD_IF) {
        handle_if_statement();
    } else if (current_token_.type == KEYWORD_WHILE) {
        handle_while_statement();
    } else if (current_token_.type == KEYWORD_FUNCTION) {
        parse_function_declaration();
    } else if (current_token_.type == KEYWORD_RETURN) {
        handle_return_statement();
        eat(SEMICOLON);
    } else if (current_token_.type == IDENTIFIER) { // Could be a function call as a statement
        // This is a simplified approach. A full parser would distinguish between
        // variable assignment and function call statements more robustly.
        Token id_token = current_token_;
        eat(IDENTIFIER);
        if (current_token_.type == LPAREN) {
            parse_function_call(id_token.value); // Call function, ignore return value if not assigned
            eat(SEMICOLON);
        } else {
            throw std::runtime_error("Syntax Error: Unexpected identifier in statement: " + id_token.to_string());
        }
    } else {
        throw std::runtime_error("Syntax Error: Unexpected token in statement: " + current_token_.to_string());
    }
}

void Interpreter::parse_program() {
    while (current_token_.type != END_OF_FILE && !returned_) {
        parse_statement();
    }
}

std::string Interpreter::interpret(const std::string& code, std::function<void(const std::string&)> print_callback) {
    Tokenizer tokenizer(code);
    tokenizer_ = &tokenizer;
    current_token_ = tokenizer_->get_next_token();
    print_callback_ = print_callback;
    // Clear and re-initialize call stack for each interpretation
    call_stack_.clear();
    call_stack_.push_back({}); // Global scope
    function_definitions_.clear();
    returned_ = false;
    return_value_ = Value();

    try {
        parse_program();
    } catch (const std::runtime_error& e) {
        return "Error: " + std::string(e.what()) + "\n";
    }
    return ""; // Output is now handled by the callback
}