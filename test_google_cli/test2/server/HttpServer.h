#ifndef HTTP_SERVER_H
#define HTTP_SERVER_H

#include "Router.h"
#include "Middleware.h"
#include "../utilities/Logger.h"
#include "../core/ThreadPool.h"
#include <atomic>
#include <string>
#include <memory>
#include <vector>
#include <chrono>
#include <unordered_map>
#include <functional>

/**
 * @brief Server configuration structure
 */
struct ServerConfig {
    std::string host = "0.0.0.0";
    int port = 8080;
    int max_connections = 1000;
    int thread_pool_size = std::thread::hardware_concurrency();
    std::chrono::seconds request_timeout{30};
    size_t max_request_size = 10 * 1024 * 1024; // 10MB
    bool enable_keep_alive = true;
    std::chrono::seconds keep_alive_timeout{60};
    std::string static_files_root = "./public";
    bool enable_compression = true;
    bool enable_logging = true;
};

/**
 * @brief Connection statistics
 */
struct ConnectionStats {
    std::atomic<size_t> total_connections{0};
    std::atomic<size_t> active_connections{0};
    std::atomic<size_t> total_requests{0};
    std::atomic<size_t> failed_requests{0};
    std::atomic<size_t> bytes_sent{0};
    std::atomic<size_t> bytes_received{0};
    std::chrono::steady_clock::time_point start_time;

    ConnectionStats() : start_time(std::chrono::steady_clock::now()) {}
};

class HttpServer {
public:
    explicit HttpServer(int port);
    explicit HttpServer(const ServerConfig& config);
    ~HttpServer();

    void start();
    void stop();
    Router& get_router();

    // Middleware management
    void use(std::shared_ptr<server::IMiddleware> middleware);
    void enableCors(const std::string& origin = "*");
    void enableLogging();
    void enableRateLimit(size_t max_requests = 100, std::chrono::seconds window = std::chrono::seconds(60));
    void enableSecurityHeaders();
    void enableStaticFiles(const std::string& root_path = "./public");
    void enableCompression();

    // Configuration
    void setConfig(const ServerConfig& config);
    const ServerConfig& getConfig() const { return config_; }

    // Statistics
    const ConnectionStats& getStats() const { return stats_; }
    std::string getStatsJson() const;

    // Error handling
    void setErrorHandler(std::function<HttpResponse(const std::exception&, const HttpRequest&)> handler);

private:
    ServerConfig config_;
    int server_fd_;
    std::atomic<bool> running_;
    Router router_;

    // Middleware chain
    std::vector<std::shared_ptr<server::IMiddleware>> middleware_chain_;

    // Thread pool for handling connections
    std::unique_ptr<core::ThreadPool> thread_pool_;

    // Logging
    std::shared_ptr<utilities::Logger> logger_;

    // Statistics
    mutable ConnectionStats stats_;

    // Error handling
    std::function<HttpResponse(const std::exception&, const HttpRequest&)> error_handler_;

    // Connection management
    std::unordered_map<int, std::chrono::steady_clock::time_point> active_connections_;
    std::mutex connections_mutex_;

    void handle_client(int client_socket);
    void process_request(int client_socket, const std::string& raw_request);
    HttpResponse execute_middleware_chain(const HttpRequest& request);
    void cleanup_connections();
    void setup_default_error_handler();
    void setup_signal_handlers();
    HttpResponse handle_error(const std::exception& e, const HttpRequest& request);

    // Socket utilities
    bool set_socket_options(int socket_fd);
    void set_socket_timeout(int socket_fd, std::chrono::seconds timeout);
};

#endif // HTTP_SERVER_H
