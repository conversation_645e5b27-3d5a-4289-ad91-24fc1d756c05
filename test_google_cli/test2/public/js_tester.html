<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JS Interpreter Tester</title>
    <link rel="stylesheet" href="/style.css">
    <style>
        textarea {
            width: 80%;
            height: 200px;
            margin-bottom: 10px;
            padding: 10px;
            font-family: monospace;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        button {
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        #output {
            margin-top: 20px;
            padding: 15px;
            background-color: #e9ecef;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            white-space: pre-wrap; /* Preserve whitespace and wrap text */
            font-family: monospace;
        }
    </style>
</head>
<body>
    <h1>Rudimentary JS Interpreter Tester</h1>
    <p>Enter your JavaScript-like code below and click "Run".</p>
    <p>Supported features: <code>let</code>, <code>print()</code>, basic arithmetic, boolean literals (<code>true</code>, <code>false</code>), comparisons (<code>==</code>, <code>!=</code>, <code><</code>, <code>></code>, <code><=</code>, <code>>=</code>), <code>if-else</code> statements, <code>while</code> loops, string literals, string concatenation, functions, logical operators (<code>&&</code>, <code>||</code>, <code>!</code>), comments (<code>//</code>, <code>/* */</code>), and <b>unary minus (<code>-</code>)</b>.</p>
    <p>Example: <code>let x = 10 + 5; if (x > 12) { print(true); } else { print(false); }</code></p>
    <p>While loop example: <code>let i = 0; while (i < 3) { print(i); i = i + 1; }</code></p>
    <p>String example: <code>let greeting = 'Hello'; let name = 'World'; print(greeting + ', ' + name + '!');</code></p>
    <p>Function example: <code>function add(a, b) { return a + b; } print(add(5, 3));</code></p>
    <p>Logical operator example: <code>print(true && false); print(!(10 > 5));</code></p>
    <p>Unary minus example: <code>let x = 10; print(-x); print(-(5 + 3));</code></p>

    <textarea id="jsCodeInput">// This is a single-line comment
/* This is a
   multi-line comment */
let a = 5;
let b = 10;
if (a < b) {
    print(a + b);
} else {
    print(a * b);
}
print(true == false);
print(10 >= 10);

let i = 0;
while (i < 3) {
    print("Loop iteration: " + i);
    i = i + 1;
}

let firstName = 'John';
let lastName = 'Doe';
print('Full Name: ' + firstName + ' ' + lastName);
print('The answer is: ' + (10 * 5));

function multiply(x, y) {
    return x * y;
}
print("Result of multiply(4, 6): " + multiply(4, 6));

function greet(name) {
    print('Hello, ' + name + '!');
}
greet('Alice');

function factorial(n) {
    if (n == 0) {
        return 1;
    } else {
        return n * factorial(n - 1);
    }
}
print("Factorial of 5: " + factorial(5));

// Logical operators
print("Logical AND (true && false): " + (true && false));
print("Logical OR (true || false): " + (true || false));
print("Logical NOT (!(10 > 5)): " + (!(10 > 5)));
print("Complex logical: " + ((5 > 3) && (10 < 20) || false));

// Unary minus
let num = 25;
print("Unary minus of num: " + (-num));
print("Unary minus of expression: " + (-(10 + 5)));
</textarea><br>
    <button id="runButton">Run Code</button>

    <h2>Output:</h2>
    <div id="output"></div>

    <p>Go back to <a href="/">home</a>.</p>

    <script>
        document.getElementById('runButton').addEventListener('click', async () => {
            const code = document.getElementById('jsCodeInput').value;
            const outputDiv = document.getElementById('output');
            outputDiv.textContent = 'Running...';

            try {
                // URL-encode the code to safely pass it as a query parameter
                const encodedCode = encodeURIComponent(code);
                const response = await fetch(`/run_js?code=${encodedCode}`);
                const text = await response.text();
                outputDiv.textContent = text;
            } catch (error) {
                outputDiv.textContent = `Error: ${error.message}`;
                console.error('Error running JS code:', error);
            }
        });
    </script>
</body>
</html>